{"name": "TechBrief45_GlobalError", "nodes": [{"parameters": {}, "id": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [200, 200]}, {"parameters": {"mode": "manual", "assignments": {"assignments": [{"name": "run_id", "type": "string", "value": "={{String(Date.now())}}"}, {"name": "topic_title", "type": "string", "value": ""}, {"name": "source", "type": "string", "value": ""}, {"name": "source_url", "type": "string", "value": ""}, {"name": "topic_score", "type": "number", "value": 0}, {"name": "hook_a", "type": "string", "value": ""}, {"name": "hook_b", "type": "string", "value": ""}, {"name": "chosen_hook", "type": "string", "value": ""}, {"name": "video_url", "type": "string", "value": ""}, {"name": "thumbnail_a_url", "type": "string", "value": ""}, {"name": "thumbnail_b_url", "type": "string", "value": ""}, {"name": "persona", "type": "string", "value": "early-career dev"}, {"name": "status", "type": "string", "value": "error"}, {"name": "errors", "type": "string", "value": "={{$json.error && $json.error.message ? $json.error.message : 'Unknown error'}}"}, {"name": "created_at", "type": "string", "value": "={{new Date().toISOString()}}"}]}, "includeOtherFields": true}, "id": "Package Error", "name": "Package Error", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 200]}, {"parameters": {"operation": "append", "documentId": {"mode": "raw", "value": "={{$json.sheets_id || '1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo'}}"}, "sheetName": {"mode": "raw", "value": "Outputs"}, "range": "Outputs!A:N"}, "id": "Append Outputs (Error)", "name": "Append Outputs (Error)", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [680, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "", "name": "Google Sheets (TechBrief45)"}}}, {"parameters": {"text": "=TechBrief45 ERROR: {{$json.errors}}"}, "id": "Slack Error Notify", "name": "Slack Error Notify", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [920, 200], "disabled": true, "credentials": {"slackApi": {"id": "", "name": "Slack (TechBrief45)"}}}], "connections": {"Error Trigger": {"main": [[{"node": "Package Error", "type": "main", "index": 0}]]}, "Package Error": {"main": [[{"node": "Append Outputs (Error)", "type": "main", "index": 0}]]}, "Append Outputs (Error)": {"main": [[{"node": "Slack Error Notify", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "pinData": {}, "staticData": null, "meta": {"workflowId": "techbrief45-error"}}