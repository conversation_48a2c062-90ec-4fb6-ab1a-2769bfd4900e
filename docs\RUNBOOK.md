Intent: Step-by-step runbook for operators to execute TechBrief45 safely with cost controls and basic troubleshooting.

## 1) One-time setup checklist
- Import both workflows (Main + GlobalError)
- Configure credentials:
  - Google Sheets (TechBrief45) OAuth2
  - OpenAI (TechBrief45) API key (only for Real mode)
  - Slack (TechBrief45) if you want notifications (optional; node disabled by default)
- Prepare Google Sheet with tabs & headers (see README)
- Verify Set Config node values (mock_mode, k, brand, sheets_id)

## 2) Dry-run (Mock Mode)
- Open TechBrief45_Main, ensure Set Config.mock_mode=true
- Execute Manual Trigger
- Expect: new row in Outputs with status=success; mock URLs for video and thumbs; and a new row in Recent for dedupe
- If you see no new row: likely deduped (topic already processed); clear Recent or wait for new topics

## 3) Switch to Real Mode (when ready)
- Set Config.mock_mode=false
- Ensure:
  - OpenAI credentials present (gpt-4o-mini)
  - HTTP Request headers for Gemini/Seedance have valid API keys (Authorization: Bearer <KEY>)
- Run Manual Trigger again
- Expect: real LLM call, image/keyframes request, render request, thumbnail generation, Outputs+Recent updates

## 4) Cost & rate-limit guardrails
- Keep k=1 during initial real runs; gradually raise when stable
- HN item fetch capped to 5 latest IDs to minimize load
- GPT maxTokens=320; prompts are compact and structured JSON for deterministic outputs
- Reddit RSS usage is light; if RSS throttles, add node-level retry with backoff

## 5) Metrics loop (MVP)
- Outputs logs: two hooks (A/B), chosen_hook currently defaults to A
- For A/B testing: publish two thumbnail variants externally and record engagement back into Outputs (add columns: views, ctr, vtr)
- Weekly: sort Outputs by created_at to analyze hook effectiveness. Choose the better hook family for next batches.

## 6) Troubleshooting
- No items selected:
  - Check TopicScore 6 Limit K results; K=1 may skip if all duplicate
- Duplicate detected:
  - Recent sheet holds hashes; clear specific row to reprocess if needed
- Sheets write fails:
  - Re-auth Google Sheets (TechBrief45); ensure spreadsheet permissions
- OpenAI errors or quota:
  - Flip mock_mode=true; reduce k; wait for quota reset
- HTTP 4xx/5xx from Gemini/Seedance:
  - Check Authorization headers and request body schema; test with Postman

## 7) Operational tips
- Keep Slack disabled until reviewers approve; then enable and choose channel
- Add a Schedule Trigger (weekly) once manual runs are consistently clean
- Snapshot/export the workflow JSON after changes for auditability

## 8) Handover notes
- Source feeds: Reddit r/technology, r/MachineLearning + HN new stories
- Dedupe: canonical title hash vs Recent
- Brand color: #1E88E5 (configurable)
- Expected runtime: < 30s in Mock Mode, varies in Real mode

