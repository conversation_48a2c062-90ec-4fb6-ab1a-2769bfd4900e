%% TechBrief 45 — n8n Workflow (Mock-first)
%% Mermaid diagram
flowchart TD
  A[Manual Trigger] --> B[Set Config: mock_mode, K=1, brand_blue, sheets_id]
  B --> C1[RSS Read: r/technology]
  B --> C2[RSS Read: r/MachineLearning]
  B --> D1[HTTP: HN newstories]
  D1 --> D2[Function: limit 5 IDs]
  D2 --> D3[HTTP: HN item detail per id]
  C1 --> E[Merge append]
  C2 --> E
  D3 --> E
  E --> F[Function: normalize topic]
  F --> G[Function: TopicScore]
  G --> H[Sort by topic_score desc]
  H --> I[Limit K]
  I --> J[Google Sheets: Read Recent]
  J --> K[Function: hash + dedupe vs Recent]
  K -->|new| L{mock_mode?}
  K -->|duplicate| X[End: Skipped]
  L -->|true| M1[Set: Mock script + assets]
  L -->|false| M2[OpenAI Chat → JSON script/storyboard]
  M2 --> N1[HTTP: Gemini keyframes]
  M2 --> N2[HTTP: Seedance 9:16 video]
  M2 --> N3[HTTP: Thumbnails A/B]
  M1 --> O[Set: Package Outputs row]
  N1 --> O
  N2 --> O
  N3 --> O
  O --> P[Google Sheets: Append Outputs]
  P --> Q[Slack Notify (optional/off by default)]
  P --> R[Google Sheets: Append Recent]

