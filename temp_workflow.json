{"name": "TechBrief45_Main", "nodes": [{"parameters": {}, "id": "32ef378c-dca1-4310-a521-542d5c22f946", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-480, 144]}, {"parameters": {"assignments": {"assignments": [{"id": "mock_mode", "name": "mock_mode", "type": "boolean", "value": false}, {"id": "k", "name": "k", "type": "number", "value": 1}, {"id": "brand_blue_hex", "name": "brand_blue_hex", "type": "string", "value": "#1E88E5"}, {"id": "sheets_id", "name": "sheets_id", "type": "string", "value": "1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo"}]}, "includeOtherFields": true, "options": {}}, "id": "9759ede3-42f2-469e-862a-b3f6db49ca48", "name": "Set Config", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-256, 144]}, {"parameters": {"resource": "chat", "chatModel": "gpt-4o-mini", "prompt": {"messages": [{}]}, "options": {}, "requestOptions": {}}, "id": "b346e61e-e306-4de8-ba2b-3f881ef2345a", "name": "OpenAI Script", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [2656, 336], "credentials": {"openAiApi": {"id": "AL9GKX0l40qALYoU", "name": "OpenAi account"}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Set Config", "type": "main", "index": 0}]]}}}