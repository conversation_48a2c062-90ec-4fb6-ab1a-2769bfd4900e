{"name": "TechBrief45_Main", "nodes": [{"parameters": {}, "id": "32ef378c-dca1-4310-a521-542d5c22f946", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-480, 144]}, {"parameters": {"assignments": {"assignments": [{"id": "mock_mode", "name": "mock_mode", "type": "boolean", "value": false}, {"id": "k", "name": "k", "type": "number", "value": 1}, {"id": "brand_blue_hex", "name": "brand_blue_hex", "type": "string", "value": "#1E88E5"}, {"id": "sheets_id", "name": "sheets_id", "type": "string", "value": "1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo"}]}, "includeOtherFields": true, "options": {}}, "id": "9759ede3-42f2-469e-862a-b3f6db49ca48", "name": "Set Config", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-256, 144]}, {"parameters": {"url": "https://www.reddit.com/r/technology/.rss", "options": {}}, "id": "9e703530-4a4e-48ae-9559-58bbdce699aa", "name": "RSS Technology", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.2, "position": [-32, -48]}, {"parameters": {"url": "https://hacker-news.firebaseio.com/v0/newstories.json?print=pretty", "options": {}}, "id": "18783b32-dc74-48e0-b51e-508c05d0a2d3", "name": "HN New IDs", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-32, 336]}, {"parameters": {"functionCode": "// Robustly take up to 5 HN IDs from common input shapes\nlet ids = [];\n\nif (Array.isArray(items) && items.length > 0) {\n  const j0 = items[0] && items[0].json;\n  \n  if (Array.isArray(j0)) {\n    ids = j0;\n  } else if (j0 && Array.isArray(j0.data)) {\n    ids = j0.data;\n  } else if (j0 && Array.isArray(j0.ids)) {\n    ids = j0.ids;\n  } else {\n    ids = items.map(it => it && it.json ? it.json.id : undefined)\n               .filter(v => v !== undefined);\n  }\n}\n\n// Convert to numbers and filter valid IDs\nids = ids.map(x => (typeof x === 'string' ? parseInt(x, 10) : x))\n         .filter(n => Number.isFinite(n));\n\n// Return top 5 IDs in n8n format\nreturn ids.slice(0, 5).map(id => ({ json: { id } }));"}, "id": "9f47e48d-01be-44a7-a926-715546ec92aa", "name": "Limit HN IDs", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [192, 336], "alwaysOutputData": false}, {"parameters": {"url": "={{'https://hacker-news.firebaseio.com/v0/item/' + $json.id + '.json'}}", "options": {}}, "id": "bc0cb389-0f58-4a7a-9a42-c5696bd33ac1", "name": "HN Item Detail", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [416, 336]}, {"parameters": {}, "id": "aa1b3081-b073-41d3-b0f7-de5a6e1659b2", "name": "Merge RSS", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [304, 48]}, {"parameters": {}, "id": "2711f6a5-df8a-47c8-aae4-d2ef7876a791", "name": "<PERSON>rge <PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [640, 144]}, {"parameters": {"functionCode": "// Normalize to {source,id_or_url,title,url,created_ts,raw_score}\nconst out = {...item};\nconst j = item.json || {};\nif (j.link && j.title) {\n  out.json = { source: 'reddit', id_or_url: j.link, title: j.title, url: j.link, created_ts: Date.parse(j.isoDate || j.pubDate || 0)/1000|0, raw_score: 0 };\n} else if (j.id && (j.title || j.text)) {\n  out.json = { source: 'hn', id_or_url: String(j.id), title: j.title || (j.text||'').slice(0,80), url: j.url || `https://news.ycombinator.com/item?id=${j.id}`, created_ts: (j.time||0), raw_score: j.score||0 };\n}\nreturn out;"}, "id": "f418bc71-8bc5-4267-9929-f8a861d46be4", "name": "Normalize Topic", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [864, 144]}, {"parameters": {"functionCode": "// Compute TopicScore with guards for missing fields\nconst j = (item && item.json) ? item.json : {};\nconst now = Date.now()/1000;\nconst created = (typeof j.created_ts === 'number' && isFinite(j.created_ts)) ? j.created_ts : now;\nconst ageH = Math.max(0, (now - created) / 3600);\nconst recency = Math.max(0, 100 - ageH);\nconst raw = (typeof j.raw_score === 'number' && isFinite(j.raw_score)) ? j.raw_score : 0;\nitem.json = { ...j, topic_score: raw + recency };\nreturn item;"}, "id": "48c4d9be-9ac2-4938-bb05-7e332324a9a6", "name": "TopicScore", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [1088, 144]}, {"parameters": {"type": "code", "code": "// Desc by topic_score\nif ((b.json.topic_score||0) < (a.json.topic_score||0)) return -1;\nif ((b.json.topic_score||0) > (a.json.topic_score||0)) return 1;\nreturn 0;"}, "id": "fb032e92-8c89-4484-9c99-2f01f0b7d421", "name": "Sort by Score", "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [1312, 144]}, {"parameters": {}, "id": "178264c5-9b44-402f-ba19-8798e1621f54", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [1536, 144]}, {"parameters": {"documentId": {"__rl": true, "value": "1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo", "mode": "list", "cachedResultName": "TechBrief", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Recent", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit#gid=**********"}, "options": {}}, "id": "ce957a78-e280-4278-b4f5-7bb7f5d00827", "name": "Read Recent", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [1760, 144], "credentials": {"googleSheetsOAuth2Api": {"id": "PiO4rDFxrvxCESzG", "name": "Google Sheets account"}}}, {"parameters": {"functionCode": "// Compute canonical title hash and mark duplicate\nconst j = (item && item.json) ? item.json : {};\nfunction canon(s){return (s||'').toLowerCase().replace(/[^a-z0-9 ]/g,' ').replace(/\\s+/g,' ').trim();}\nfunction hash(s){let h=5381; for (let i=0;i<s.length;i++){h=((h<<5)+h)+s.charCodeAt(i); h&=0xffffffff;} return String(h>>>0);}\nconst title = j.title || '';\nconst ch = hash(canon(title));\nitem.json = { ...j, canonical_title_hash: ch };\n// If Read Recent produced rows with hashes in column B, build a Set\nconst rec = $items('Read Recent', 0, 0) || [];\nconst hashes = new Set(rec.map(r => (r.json && (r.json[1] || r.json.hash || ''))));\nitem.json.is_duplicate = hashes.has(ch);\nreturn item;"}, "id": "87fa203a-501b-48dc-8742-3fb6b759e4ca", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [1984, 144]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.is_duplicate}}", "operation": "isFalse"}]}, "options": {}}, "id": "02b5b0df-0397-4322-a7fa-b86b0f224a25", "name": "If New", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2208, 144]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bd4bba1d-8773-4c47-aaf0-75a2d0668424", "leftValue": "={{ $('Set Config').item.json.mock_mode }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "a243d384-e6b9-4a74-951b-68fc048975a2", "name": "If Mock Mode", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2432, 144]}, {"parameters": {"assignments": {"assignments": [{"name": "script.hook", "type": "string", "value": "Breaking: <title> in 45 seconds"}, {"name": "script.value", "type": "string", "value": "Why it matters and what to watch."}, {"name": "script.cta", "type": "string", "value": "Read the source: <url>"}, {"name": "hooks.a", "type": "string", "value": "Dev heads-up: <title> in 45s"}, {"name": "hooks.b", "type": "string", "value": "What <topic> means for you (45s)"}, {"name": "video_url", "type": "string", "value": "https://example.com/mock/video.mp4"}, {"name": "thumbnail_a_url", "type": "string", "value": "https://example.com/mock/thumb_a.jpg"}, {"name": "thumbnail_b_url", "type": "string", "value": "https://example.com/mock/thumb_b.jpg"}]}, "includeOtherFields": true, "options": {}}, "id": "1691acfc-7f41-4032-bc6d-e2f35cf1176d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2880, -80]}, {"parameters": {"resource": "chat", "chatModel": "gpt-4o-mini", "prompt": {"messages": [{}]}, "options": {}, "requestOptions": {}}, "id": "b346e61e-e306-4de8-ba2b-3f881ef2345a", "name": "OpenAI Script", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [2656, 336], "credentials": {"openAiApi": {"id": "AL9GKX0l40qALYoU", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "authentication": "predefinedCredentialType", "nodeCredentialType": "googlePalmApi", "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {}}, "id": "59ab86e5-0898-42fd-9d54-31d3e7bc2a4f", "name": "Gemini Keyframes", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2880, 144], "credentials": {"httpBearerAuth": {"id": "DUOIwFXE9aQQFQzx", "name": "Bear<PERSON> account 2"}, "googlePalmApi": {"id": "ssQ1Vr5xamgAeRP3", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"method": "POST", "url": "https://api.seedance.example.com/v1/render", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBearerAuth", "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {}}, "id": "fd08d7f0-c172-4576-b729-1ecfec738ff2", "name": "Seedance Render", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2880, 336], "credentials": {"httpBearerAuth": {"id": "XNm5Jx6XBItdh6mq", "name": "Bearer Auth account"}}}, {"parameters": {"method": "POST", "url": "https://generative.googleapis.com/v1beta/images:generate", "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {}}, "id": "7e457bd0-ac1f-4c8a-ba7d-eaf36a223ea2", "name": "Thumbnails", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2880, 528]}, {"parameters": {"assignments": {"assignments": [{"name": "run_id", "type": "string", "value": "={{String(Date.now())}}"}, {"name": "topic_title", "type": "string", "value": "={{$json.title}}"}, {"name": "source", "type": "string", "value": "={{$json.source}}"}, {"name": "source_url", "type": "string", "value": "={{$json.url}}"}, {"name": "topic_score", "type": "number", "value": "={{$json.topic_score}}"}, {"name": "hook_a", "type": "string", "value": "={{$json.hooks && $json.hooks.a ? $json.hooks.a : 'N/A'}}"}, {"name": "hook_b", "type": "string", "value": "={{$json.hooks && $json.hooks.b ? $json.hooks.b : 'N/A'}}"}, {"name": "chosen_hook", "type": "string", "value": "={{$json.hooks && $json.hooks.a ? $json.hooks.a : 'A'}}"}, {"name": "video_url", "type": "string", "value": "={{$json.video_url || ''}}"}, {"name": "thumbnail_a_url", "type": "string", "value": "={{$json.thumbnail_a_url || ''}}"}, {"name": "thumbnail_b_url", "type": "string", "value": "={{$json.thumbnail_b_url || ''}}"}, {"name": "persona", "type": "string", "value": "early-career dev"}, {"name": "status", "type": "string", "value": "success"}, {"name": "errors", "type": "string", "value": ""}, {"name": "created_at", "type": "string", "value": "={{new Date().toISOString()}}"}, {"name": "created_at_iso", "type": "string", "value": "={{new Date().toISOString()}}"}]}, "includeOtherFields": true, "options": {}}, "id": "2d89887b-0df4-4193-a535-94d53afa79f2", "name": "Package Outputs", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [3104, 240]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo", "mode": "list", "cachedResultName": "TechBrief", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 819066079, "mode": "list", "cachedResultName": "Outputs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit#gid=819066079"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": [], "schema": [{"id": "run_id", "displayName": "run_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "topic_title", "displayName": "topic_title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "source", "displayName": "source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "source_url", "displayName": "source_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "topic_score", "displayName": "topic_score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "hook_a", "displayName": "hook_a", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "hook_b", "displayName": "hook_b", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "chosen_hook", "displayName": "chosen_hook", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "video_url", "displayName": "video_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "thumbnail_a_url", "displayName": "thumbnail_a_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "thumbnail_b_url", "displayName": "thumbnail_b_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "persona", "displayName": "persona", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "errors", "displayName": "errors", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at", "displayName": "created_at", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "34dfd517-a5f2-46d1-9ab0-3ec000d4c4fa", "name": "Append Outputs", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [3328, 240], "credentials": {"googleSheetsOAuth2Api": {"id": "PiO4rDFxrvxCESzG", "name": "Google Sheets account"}}}, {"parameters": {"text": "=TechBrief45: {{$json.topic_title}}\nVideo: {{$json.video_url}}\nThumbs: {{$json.thumbnail_a_url}}, {{$json.thumbnail_b_url}}", "otherOptions": {}}, "id": "60a0e31e-cbb1-42c7-ab51-376ea23f84ad", "name": "Slack Notify", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3552, 144], "webhookId": "f926b089-63c4-4f33-a1fa-8c52eb53fb1b", "disabled": true}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo", "mode": "list", "cachedResultName": "TechBrief", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Recent", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo/edit#gid=**********"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": [], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "canonical_title_hash", "displayName": "canonical_title_hash", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at_iso", "displayName": "created_at_iso", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "1aba1415-f880-4d1e-affa-f153e8a9eddc", "name": "Append Recent", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [3552, 336], "credentials": {"googleSheetsOAuth2Api": {"id": "PiO4rDFxrvxCESzG", "name": "Google Sheets account"}}}, {"parameters": {"url": "https://www.reddit.com/r/ArtificialInteligence/.rss", "options": {}}, "id": "daedbfaf-39f4-4dbb-9b4b-0d9543e8d2c7", "name": "RSS ArtificialInteligence", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.2, "position": [-32, 144]}], "pinData": {}, "connections": {"Set Config": {"main": [[{"node": "RSS Technology", "type": "main", "index": 0}, {"node": "RSS ArtificialInteligence", "type": "main", "index": 0}, {"node": "HN New IDs", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Set Config", "type": "main", "index": 0}]]}, "RSS Technology": {"main": [[{"node": "Merge RSS", "type": "main", "index": 0}]]}, "HN New IDs": {"main": [[{"node": "Limit HN IDs", "type": "main", "index": 0}]]}, "Limit HN IDs": {"main": [[{"node": "HN Item Detail", "type": "main", "index": 0}]]}, "HN Item Detail": {"main": [[{"node": "<PERSON>rge <PERSON>", "type": "main", "index": 1}]]}, "Merge RSS": {"main": [[{"node": "<PERSON>rge <PERSON>", "type": "main", "index": 0}]]}, "Merge All": {"main": [[{"node": "Normalize Topic", "type": "main", "index": 0}]]}, "Normalize Topic": {"main": [[{"node": "TopicScore", "type": "main", "index": 0}]]}, "TopicScore": {"main": [[{"node": "Sort by Score", "type": "main", "index": 0}]]}, "Sort by Score": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Limit K": {"main": [[{"node": "Read Recent", "type": "main", "index": 0}]]}, "Read Recent": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Dedupe": {"main": [[{"node": "If New", "type": "main", "index": 0}]]}, "If New": {"main": [[{"node": "If Mock Mode", "type": "main", "index": 0}]]}, "If Mock Mode": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "OpenAI Script", "type": "main", "index": 0}]]}, "Mock Assets": {"main": [[{"node": "Package Outputs", "type": "main", "index": 0}]]}, "OpenAI Script": {"main": [[{"node": "Gemini Keyframes", "type": "main", "index": 0}, {"node": "Seedance Render", "type": "main", "index": 0}, {"node": "Thumbnails", "type": "main", "index": 0}]]}, "Gemini Keyframes": {"main": [[{"node": "Package Outputs", "type": "main", "index": 0}]]}, "Seedance Render": {"main": [[{"node": "Package Outputs", "type": "main", "index": 0}]]}, "Thumbnails": {"main": [[{"node": "Package Outputs", "type": "main", "index": 0}]]}, "Package Outputs": {"main": [[{"node": "Append Outputs", "type": "main", "index": 0}]]}, "Append Outputs": {"main": [[{"node": "Slack Notify", "type": "main", "index": 0}, {"node": "Append Recent", "type": "main", "index": 0}]]}, "RSS ArtificialInteligence": {"main": [[{"node": "Merge RSS", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5de77dfc-215c-4019-80a6-86af0b687a13", "meta": {"instanceId": "647892562f323e2a111bec7211243f18590a0feae561daaf0a6d4a8dbb6e302a"}, "id": "RIOLzZjRjqu84JLn", "tags": []}