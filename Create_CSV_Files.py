# Create CSV templates for Google Sheets for TechBrief 45
import pandas as pd
from datetime import datetime
import os

# Inputs sheet template
inputs_cols = [
    "source",           # hn | reddit | rss | manual
    "id_or_url",        # HN item id or full URL (reddit post, blog)
    "title",
    "persona",          # student | dev | pm (free text ok)
    "angle",            # e.g., 'why it matters for devs'
    "cta_url",          # link in bio or site
    "priority",         # A | B | C
    "status",           # queued | picked | done | error
    "notes"
]
inputs = pd.DataFrame([{
    "source": "hn",
    "id_or_url": "42013337",
    "title": "Example: New open-source LLM beats baseline on coding tasks",
    "persona": "dev",
    "angle": "What changes for junior devs",
    "cta_url": "https://humai.ai/techbrief",
    "priority": "A",
    "status": "queued",
    "notes": "Replace with a real item; can also POST via webhook"
}] , columns=inputs_cols)

# Outputs sheet template
outputs_cols = [
    "run_id",
    "topic_title",
    "source",
    "source_url",
    "topic_score",
    "hook_a",
    "hook_b",
    "chosen_hook",
    "video_url",
    "thumbnail_a_url",
    "thumbnail_b_url",
    "persona",
    "status",
    "errors",
    "created_at"
]
outputs = pd.DataFrame([{
    "run_id": "tb45_0001",
    "topic_title": "Example: New open-source LLM beats baseline on coding tasks",
    "source": "hn",
    "source_url": "https://news.ycombinator.com/item?id=42013337",
    "topic_score": 0.78,
    "hook_a": "Why everyone is talking about X: it changes how your apps reason.",
    "hook_b": "This release fixes the worst pain devs have with agents—here’s how.",
    "chosen_hook": "hook_a",
    "video_url": "https://drive.google.com/file/d/EXAMPLE",
    "thumbnail_a_url": "https://drive.google.com/file/d/THUMB_A",
    "thumbnail_b_url": "https://drive.google.com/file/d/THUMB_B",
    "persona": "dev",
    "status": "done",
    "errors": "",
    "created_at": datetime.utcnow().isoformat(timespec='seconds') + "Z"
}], columns=outputs_cols)

# Recent topics sheet template (for dedupe)
recent_cols = ["title", "canonical_title_hash", "created_at_iso"]
recent = pd.DataFrame([{
    "title": "Example: New open-source LLM beats baseline on coding tasks",
    "canonical_title_hash": "c7b5b9bf",
    "created_at_iso": datetime.utcnow().isoformat(timespec='seconds') + "Z"
}], columns=recent_cols)

# Create CSV_Files directory if it doesn't exist
os.makedirs("./CSV_Files", exist_ok=True)

# Save to CSVs
inputs_path = "./CSV_Files/techbrief45_inputs.csv"
outputs_path = "./CSV_Files/techbrief45_outputs.csv"
recent_path = "./CSV_Files/techbrief45_recent.csv"

inputs.to_csv(inputs_path, index=False)
outputs.to_csv(outputs_path, index=False)
recent.to_csv(recent_path, index=False)

print(f"Created CSV files:")
print(f"- {inputs_path}")
print(f"- {outputs_path}")  
print(f"- {recent_path}")
