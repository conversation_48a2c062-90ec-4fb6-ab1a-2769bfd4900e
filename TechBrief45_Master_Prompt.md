# TechBrief 45 — Master Context for Augment Code (n8n‑mcp)

**Last updated:** 2025-09-17 07:54 UTC
**Owner:** Umar
**Objective:** Provide a single, comprehensive context pack for Augment Code (with n8n‑mcp) to build **TechBrief 45** end‑to‑end.

## Current Status (Revamp in progress)

- n8n Workflows: TechBrief45_Main.json and TechBrief45_GlobalError.json created.
- Mock mode: end-to-end validated; Outputs and Recent append successfully.
- Real mode routing: fix needed. LLM, thumbnail, and video generation not configured correctly
- Append nodes: switched to auto-mapping; header-driven; resilient to missing fields.
- Scoring/dedupe: hardened TopicScore and canonical title hash with guards.
- Models: OpenAI gpt-4o-mini for scripting; K=1; token/cost controls enabled.
- Notifications: Slack disabled by default; can be enabled later.
- Migration: Replacing Seedance with Gemini Veo 3 for video; Gemini also for thumbnails.
- Sheets: Using Google Sheets spreadsheet and named sheets (Inputs, Outputs, Recent).

---

## 1) Role & Goal

**Role for Augment Code:** Senior Automation Engineer using **n8n‑mcp** to design, assemble, and document a production‑like workflow.

**Primary Goal:** Ship a working (or convincingly mocked) **n8n** automation that turns fresh **AI/tech news** into a **30–45 second** vertical video with consistent branding and clear source attribution. The deliverable must meet a hiring assessment’s “great” bar: persona‑aware, metrics‑ready, reproducible, and with error handling.

---

## 2) Product Context

- **Product name:** TechBrief 45
- **Customer Value Proposition (CVP):** _One AI/tech story, why it matters, in 45 seconds—with sources._
- **Persona (default):** Early‑career developer / CS student.
- **Primary channels:** TikTok, Instagram Reels, YouTube Shorts.
- **Brand system:** Colors — blue and white. Use a configurable brand blue (hex) throughout assets and prompts.
- **Core output:** A single 9:16, 1080p, multi‑shot short‑form video per topic with captions, watermark, end‑slate, and two thumbnail variants.

---

## 3) Success Criteria (mapped to assessment rubric)

1. **Running flow or faithful mock** with clear payloads and retries.
2. **Persona‑aware content**: hook → value → CTA tailored to an early‑career dev audience.
3. **Business/metrics mindset**: A/B hook testing, Outputs log for view/CTR later.
4. **Technical creativity**: Topic scoring, dedupe guard, multi‑shot composition, branded frames.
5. **Reproducibility**: Document every node’s purpose and the rationale; include a single‑page diagram and screenshots.

---

## 4) Non‑Goals & Constraints

- No sensational claims; maintain neutral professional tone.
- No sensitive data/PII; public sources only.
- Keep the scope to one final video example plus the documented pipeline.
- Ads loop is **out of scope** for V1 (saved for V2).

---

## 5) Tooling & Integrations (high‑level)

- **n8n**: Workflow orchestration (standard nodes only).
- **Data sources**:
  - Hacker News official API (for timely items and score/time).
  - Reddit public RSS feeds (e.g., r/technology, r/ArtificialIntelligence).
  - Optional: additional RSS sources can be added later with the same ingest node.
- **LLM**: Use a low‑cost, capable model in the OpenAI family for scripting and storyboard generation (e.g., gpt-4o-mini)
- **Images & thumbnails**: **Google Gemini Veo 3**.
- **Video**: **Google Gemini Veo 3** (multi‑shot, 1080p, 9:16).
- **Storage & logs**: Google Sheets (control plane + logbook). Google Drive (optional) for asset storage.
- **Notification**: Slack or Email (optional).

> The implementation should support a **mock mode** that skips third‑party generation but still logs outputs, enabling rapid end‑to‑end testing.

---

## 6) Google Sheets — Purpose & Schemas (no code)

**Why Sheets?**
Serves as the human‑friendly control plane and the immutable log for each run; supports the assignment’s requirement to ingest via Sheets and to document outputs and metrics.

**A) Inputs (editorial queue)**

- Columns (minimum): `source`, `id_or_url`, `title`, `persona`, `angle`, `cta_url`, `priority`, `status`, `notes`.
- Behavior: n8n may append candidate topics here or an editor may seed items manually.

**B) Outputs (run log)**

- Columns (minimum): `run_id`, `topic_title`, `source`, `source_url`, `topic_score`, `hook_a`, `hook_b`, `chosen_hook`, `video_url`, `thumbnail_a_url`, `thumbnail_b_url`, `persona`, `status`, `errors`, `created_at`.
- Behavior: every successful (or failed) render appends a row.

**C) Recent (dedupe memory)**

- Columns: `title`, `canonical_title_hash`, `created_at_iso`.
- Behavior: check against this sheet to avoid repeating similar topics.

---

## 7) Workflow Overview (end‑to‑end)

**Trigger** → **Ingest** (HN + Reddit RSS + optional manual queue) → **Normalize & TopicScore** → **Dedupe vs Recent** → **LLM (script, shots, two hooks)** → **Hook A/B selection** → **Gemini Veo 3 video render** → **Thumbnails generated by Gemini (2x)** → **Package & log to Sheets** → **Notify** → **Append to Recent** → **Error workflow** (for any failure).

---

## 8) Node‑by‑Node Plan (descriptive)

1. **Trigger**

   - Manual trigger for testing; Cron schedule for periodic runs (e.g., every 2 hours) once stable.

2. **Ingest: Reddit (RSS Read)**

   - Add at least two feeds (r/technology, r/MachineLearning). Return all items.
   - Output fields expected: title, link/URL, published timestamp.

3. **Ingest: Hacker News**

   - Fetch new story IDs, then fetch item details for a small top batch.
   - Output fields expected: id, title, url, time (epoch seconds), score.

4. **Merge & Normalize**

   - Merge Reddit and HN streams into a unified structure: `source`, `id_or_url`, `title`, `url`, `created_ts`, `raw_score`.

5. **TopicScore**

   - Compute a simple priority score combining recency and engagement (HN points/time; Reddit by recency).
   - Sort descending. Keep top K (start with 1 for demo).

6. **Dedupe vs Recent**

   - Compute a canonical title hash and compare against the Recent sheet to skip repeats.

7. **LLM: Script & Storyboard**

   - Prompt for a JSON structure containing:
     - **Script**: HOOK (≤5s), VALUE (25–30s), CTA (5–8s).
     - **Shots**: 4–6 shot descriptions with target durations and brand notes.
     - **Hooks**: two alternative hooks for A/B.
     - **On‑screen text** aligned to each shot.
   - Persona set to early‑career dev; reflect brand tone (clean, neutral, factual).

8. **Hook A/B Selection**

   - Rank hooks by brevity and presence of action verbs; select A/B and a default winner.
   - Log both variants to Outputs for later CTR analysis.

9. **Captions**

   - Build simple time‑boxed captions from the script (no need for perfect alignment in V1).

10. **Images (optional keyframes): Gemini Veo 3**

    - Generate consistent brand keyframes for each shot prompt.
    - Enforce vertical 9:16, clean white background with Humai blue accents.
    - Avoid faces; opt for iconography/diagrams.

11. **Video: Gemini Veo 3**

    - Multi‑shot composition, 1080p, 9:16.
    - Provide per‑shot durations and asset references; request subtle camera motion.
    - Add watermark and end‑slate with the original source URL.

12. **Thumbnails (2 variants)**

    - Generate two poster images using the two hooks (large, legible title text).

13. **Package & Store**

    - Upload or store links (Drive optional) and assemble all URLs.

14. **Append to Sheets (Outputs)**

    - Write a complete row with links, hooks, topic score, and status.

15. **Notify**

    - Send a concise Slack/Email message with the video and thumbnail links.

16. **Append to Sheets (Recent)**

    - Log the title and hash for dedupe on subsequent runs.

17. **Error Workflow**
    - On any failure, log to Outputs with `status=error` and an error message; notify optionally.

---

## 9) Mock Mode vs Real Mode

- **Mock mode:** Skip external generation and return well‑formed placeholders; still write Outputs and Recent.
- **Real mode:** Use live services for LLM, image, and video generation.

Switch mode via a single configuration flag; keep logging behavior identical in both modes.

---

## 10) Branding Guidelines

- **Palette:** Professional blue and white (Humai's brand colours); keep contrast high for caption readability.
- **Typography:** Clean, bold titles for hooks; short line lengths.
- **Watermark:** “TechBrief 45 · humai.ai” on the lower corner, small font preffered.
- **End‑slate:** Always show the original source URL.

---

## 11) Testing & Acceptance

**Unit‑like checks**

- Ingest returns at least one candidate topic.
- TopicScore is computed and sorted correctly.
- Dedupe removes a known duplicate.
- LLM returns a well‑structured JSON with script, shots, hooks.
- Hook A/B selection produces two variants and a default winner.
- Outputs and Recent sheets append rows as expected.

**Dry run (mock)**

- End‑to‑end pass producing placeholder video/thumb links and a complete Outputs row.

**Live run**

- One 30–45s 9:16 video rendered with captions, watermark, and end‑slate citing the source.

**Acceptance**

- All above completed; workflow exported; docs + screenshots ready for submission.

---

## 12) Documentation Deliverables (for submission)

**Note:** These are deliverables that I (the user) should submit to Humai as part of the assessment. Some of these may be work that I should do manually/personally. Please let me know my part.

- **One‑pager diagram** plus node‑by‑node rationale.
- **Workflow export** (JSON).
- **Screenshots** of each n8n node’s configuration.
- **One example short** (video link) and **two thumbnails**.
- **Sheets snapshots**: Inputs/Outputs/Recent.
- **Brief metrics plan**: View‑through %, CTR to source, D1 (subscribe), D7 (3+ clips).

---

## 13) Security & Secrets

- Use environment variables or n8n credentials for API keys.
- Do not commit secrets.
- Use a Google Service Account for Sheets/Drive and share access at the document/folder level.

---

## 14) Inputs the User Will Provide

**Note:** The user should provide the following. If not provided, use placeholders but, **mention**to the user to fill/replace it.

- **API keys:**

1. OpenAI API key: ********************************************************************************************************************************************************************
2. Gemini API key: AIzaSyDBrqELlwDaPCXSkq-XiEWSgPFtn1_R2LY.

- **Google Service Account JSON** and a **Spreadsheet ID** with three sheets: Inputs, Outputs, Recent.
- Optionally: a Drive folder ID and a Slack webhook URL.
- Confirm the brand blue hex if different from the default.

---

## 15) Out‑of‑Scope for V1 (nice‑to‑have later)

- Ads campaign generation (V2).
- Automatic CTR ingestion from platforms.
- Embeddings‑based novelty scoring (can be added in V1.1).
- Multi‑language narration or TTS.

---

## 16) Work Plan

1. **Scaffold repository** with config, data, prompts, scripts, workflows, and docs folders.
2. **Create three Google Sheets** (or instruct the user to create and share them; then connect n8n).
3. **Assemble the n8n workflow** exactly per the Node‑by‑Node Plan, including a **Global Error Workflow**.
4. **Implement mock mode** to validate the pipeline before real credentials.
5. **Run a dry mock** and append to Outputs; capture screenshots.
6. **Switch to real mode**, execute once, and write a final Outputs row with real links.
7. **Export the workflow JSON** and prepare documentation.

---

## 17) Acceptance Phrase

When complete, respond with a concise summary including:

- Location of the repo and workflow export,
- Links to the created sheets,
- Confirmation of one mock and one real run appended to Outputs,
- Pointers to screenshots and the one‑pager diagram.
