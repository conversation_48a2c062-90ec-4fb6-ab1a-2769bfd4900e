Intent: Provide clear setup, import, and run instructions for TechBrief45 n8n workflows with mock-first mode, cost controls, and reproducibility for weekly runs.

## What this delivers
- TechBrief45_Main.json: end-to-end workflow from ingest → score → dedupe → (mock/real) → Sheets log (+ optional Slack)
- TechBrief45_GlobalError.json: global error logger to Sheets (+ optional Slack)
- prompts/llm_storyboard_prompt.md: JSON-structured prompt contract for script/storyboard
- docs/diagram.mmd: architecture diagram

## Prerequisites
- n8n Cloud workspace (or self-host)
- Google account with access to the spreadsheet
- Spreadsheet ID (provided by you): 1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo
- Tabs in the spreadsheet (create if missing):
  - Recent (A:C) — headers: title | canonical_title_hash | created_at_iso
  - Outputs (A:N) — headers: run_id | topic_title | source | source_url | topic_score | hook_a | hook_b | chosen_hook | video_url | thumbnail_a_url | thumbnail_b_url | persona | status | errors | created_at | created_at_iso

## Import the workflows
1) In n8n, Workflows → Import from File → select workflows/TechBrief45_GlobalError.json
2) Import workflows/TechBrief45_Main.json
3) Open TechBrief45_Main and confirm node names match (used in expressions).

## Credentials to set up (mock-first)
- Google Sheets (OAuth2): create credentials in n8n named "Google Sheets (TechBrief45)"
- OpenAI (for real mode later): create credentials named "OpenAI (TechBrief45)" with your key; model used: gpt-4o-mini
- Slack (optional later): create credentials named "Slack (TechBrief45)"; node stays disabled until you enable
- Gemini & Seedance (placeholders): kept as HTTP Request nodes; only needed in Real mode

## Configure initial settings (Set Config node)
- mock_mode: true (dry-run without external API costs)
- k: 1 (only the top topic per run)
- brand_blue_hex: #1E88E5 (default)
- sheets_id: 1FK3aQI_CpM_1S1yWZihBn9wwqK9eojOMbtn0VC22KHo

## Run a dry run (Mock Mode)
- Trigger: Manual Trigger
- Sources pulled:
  - Reddit RSS: r/technology, r/MachineLearning
  - Hacker News: 5 newest item IDs → details for each
- The workflow normalizes, scores, sorts, limits to K=1, dedupes via Recent sheet, then:
  - If new: uses mock Script/Assets, packages Outputs, appends to Outputs, then appends hash to Recent
  - If duplicate: stops (no writes)
- Slack node is disabled by default.

## Where to see results
- Google Sheet → Outputs tab: new row with status=success and mock URLs
- Google Sheet → Recent tab: added hash for future dedupe

## Switch to Real Mode (after initial validation)
- In Set Config, set mock_mode=false
- Ensure credentials are present:
  - OpenAI (TechBrief45)
  - Gemini HTTP key (insert via n8n HTTP Request headers, e.g., Authorization: Bearer <KEY>)
  - Seedance HTTP key (headers)
- Token/cost control:
  - OpenAI maxTokens set to 320; keep topics K=1 while validating
  - Hacker News IDs limited to 5 upstream; expand gradually if needed

## Rate limiting & resilience
- Cost control knobs: mock_mode, K=1, HN IDs=5, GPT maxTokens=320
- Deduping prevents repeat processing
- You may enable retry/timeouts per HTTP node if needed (Options in nodes)
- Global error workflow logs any failure to Outputs with status=error (when used as an error workflow)

## How to re-run weekly
- Use Manual Trigger or Schedule Trigger (replace manual) for weekly cadence
- Keep mock_mode=true for smoke checks; flip to false for production runs
- Archive Outputs sheet weekly or add a filter view to analyze engagement metrics

## Optional next integrations
- Publishing to YouTube Shorts/TikTok API (later)
- Slack notification enablement
- A/B routing and metrics attribution (e.g., chosen_hook vs hook_a/hook_b CTR)

## Diagram
- See docs/diagram.mmd (Mermaid). In GitHub, render with a Mermaid viewer or copy into mermaid.live.

